#!/usr/bin/env python3
"""
UniXcoder RAG系统质量评估测试
评估输出内容的完善性和准确性
"""

import sys
import os
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Tuple
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class RAGQualityTester:
    """RAG系统质量测试器"""
    
    def __init__(self):
        self.test_results = []
        self.api_service = None
        self.setup_api()
    
    def setup_api(self):
        """设置API服务"""
        try:
            sys.path.insert(0, str(project_root / "frontend"))
            from api_service import get_api_instance
            self.api_service = get_api_instance()
            print("✅ API服务初始化成功")
        except Exception as e:
            print(f"❌ API服务初始化失败: {e}")
            self.api_service = None
    
    def get_test_queries(self) -> List[Dict[str, Any]]:
        """获取测试查询集合"""
        return [
            {
                "query": "代码分块算法是如何工作的？",
                "category": "算法原理",
                "expected_keywords": ["AST", "语法树", "分块", "chunk", "递归", "节点"],
                "expected_files": ["chunker.py", "ast_chunker.py"],
                "complexity": "medium"
            },
            {
                "query": "如何实现语义搜索功能？",
                "category": "功能实现",
                "expected_keywords": ["embedding", "向量", "相似度", "UniXcoder", "模型"],
                "expected_files": ["semantic_retriever.py", "unixcoder_model.py"],
                "complexity": "high"
            },
            {
                "query": "混合检索器的工作原理是什么？",
                "category": "架构设计",
                "expected_keywords": ["hybrid", "混合", "权重", "融合", "排序"],
                "expected_files": ["hybrid_retriever.py"],
                "complexity": "high"
            },
            {
                "query": "如何优化检索性能？",
                "category": "性能优化",
                "expected_keywords": ["缓存", "索引", "并行", "优化", "性能"],
                "expected_files": ["performance", "cache", "index"],
                "complexity": "medium"
            },
            {
                "query": "FAISS索引是如何构建的？",
                "category": "技术细节",
                "expected_keywords": ["FAISS", "索引", "向量", "构建", "维度"],
                "expected_files": ["build_index.py", "faiss"],
                "complexity": "medium"
            },
            {
                "query": "系统的配置文件有哪些参数？",
                "category": "配置管理",
                "expected_keywords": ["配置", "参数", "config", "设置"],
                "expected_files": ["config.py", "config.json"],
                "complexity": "low"
            },
            {
                "query": "如何处理代码重复和去重？",
                "category": "数据处理",
                "expected_keywords": ["去重", "重复", "哈希", "相似度"],
                "expected_files": ["deduplication", "hash"],
                "complexity": "medium"
            },
            {
                "query": "前端界面是如何实现流式输出的？",
                "category": "前端技术",
                "expected_keywords": ["流式", "Streamlit", "实时", "输出"],
                "expected_files": ["app.py", "frontend"],
                "complexity": "medium"
            }
        ]
    
    def evaluate_response_quality(self, query: str, response: str, search_results: List, 
                                expected_keywords: List[str], expected_files: List[str]) -> Dict[str, Any]:
        """评估响应质量"""
        evaluation = {
            "query": query,
            "response_length": len(response),
            "keyword_coverage": 0.0,
            "file_relevance": 0.0,
            "completeness_score": 0.0,
            "accuracy_score": 0.0,
            "overall_score": 0.0,
            "details": {}
        }
        
        # 1. 关键词覆盖率评估
        found_keywords = []
        for keyword in expected_keywords:
            if keyword.lower() in response.lower():
                found_keywords.append(keyword)
        
        evaluation["keyword_coverage"] = len(found_keywords) / len(expected_keywords) if expected_keywords else 0
        evaluation["details"]["found_keywords"] = found_keywords
        evaluation["details"]["missing_keywords"] = [k for k in expected_keywords if k not in found_keywords]
        
        # 2. 文件相关性评估
        relevant_files = []
        for result in search_results:
            file_path = result.file_path if hasattr(result, 'file_path') else str(result.get('file_path', ''))
            for expected_file in expected_files:
                if expected_file.lower() in file_path.lower():
                    relevant_files.append(file_path)
                    break
        
        evaluation["file_relevance"] = len(relevant_files) / len(expected_files) if expected_files else 0
        evaluation["details"]["relevant_files"] = relevant_files
        
        # 3. 完整性评估
        completeness_factors = [
            len(response) > 100,  # 回答长度足够
            "```" in response or "代码" in response,  # 包含代码示例
            "实现" in response or "方法" in response,  # 包含实现方法
            len(search_results) > 0  # 有搜索结果支撑
        ]
        evaluation["completeness_score"] = sum(completeness_factors) / len(completeness_factors)
        
        # 4. 准确性评估（基于搜索结果质量）
        if search_results:
            avg_similarity = sum(getattr(r, 'similarity', 0) for r in search_results) / len(search_results)
            high_quality_results = sum(1 for r in search_results if getattr(r, 'similarity', 0) > 0.7)
            accuracy_factors = [
                avg_similarity > 0.6,  # 平均相似度高
                high_quality_results > 0,  # 有高质量结果
                len(search_results) >= 3,  # 结果数量充足
                evaluation["keyword_coverage"] > 0.5  # 关键词覆盖率高
            ]
            evaluation["accuracy_score"] = sum(accuracy_factors) / len(accuracy_factors)
        else:
            evaluation["accuracy_score"] = 0.0
        
        # 5. 综合评分
        weights = {
            "keyword_coverage": 0.3,
            "file_relevance": 0.2,
            "completeness_score": 0.3,
            "accuracy_score": 0.2
        }
        
        evaluation["overall_score"] = sum(
            evaluation[metric] * weight for metric, weight in weights.items()
        )
        
        return evaluation
    
    def run_single_test(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """运行单个测试用例"""
        print(f"\n🧪 测试: {test_case['query']}")
        print(f"   类别: {test_case['category']} | 复杂度: {test_case['complexity']}")
        
        if not self.api_service:
            return {"error": "API服务未初始化"}
        
        try:
            # 执行查询
            from api_service import QueryRequest
            request = QueryRequest(query=test_case["query"])
            
            response_content = ""
            search_results = []
            
            # 收集流式响应
            for chunk in self.api_service.query_with_streaming(request):
                chunk_type = chunk.get('type')
                content = chunk.get('content')
                
                if chunk_type == 'search_results':
                    search_results = content
                elif chunk_type == 'content':
                    response_content += content
                elif chunk_type == 'done':
                    break
                elif chunk_type == 'error':
                    return {"error": f"查询错误: {content}"}
            
            # 评估响应质量
            evaluation = self.evaluate_response_quality(
                test_case["query"],
                response_content,
                search_results,
                test_case["expected_keywords"],
                test_case["expected_files"]
            )
            
            # 添加测试用例信息
            evaluation.update({
                "category": test_case["category"],
                "complexity": test_case["complexity"],
                "search_results_count": len(search_results),
                "response_content": response_content[:500] + "..." if len(response_content) > 500 else response_content
            })
            
            # 打印结果
            print(f"   📊 综合评分: {evaluation['overall_score']:.2f}")
            print(f"   🔑 关键词覆盖: {evaluation['keyword_coverage']:.2f}")
            print(f"   📁 文件相关性: {evaluation['file_relevance']:.2f}")
            print(f"   ✅ 完整性: {evaluation['completeness_score']:.2f}")
            print(f"   🎯 准确性: {evaluation['accuracy_score']:.2f}")
            
            return evaluation
            
        except Exception as e:
            error_result = {
                "error": str(e),
                "query": test_case["query"],
                "category": test_case["category"]
            }
            print(f"   ❌ 测试失败: {e}")
            return error_result
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        print("🚀 开始RAG系统质量评估")
        print("=" * 60)
        
        test_cases = self.get_test_queries()
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n[{i}/{len(test_cases)}]", end="")
            result = self.run_single_test(test_case)
            results.append(result)
            
            # 短暂等待，避免API限制
            time.sleep(1)
        
        # 生成综合报告
        report = self.generate_report(results)
        return report
    
    def generate_report(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 RAG系统质量评估报告")
        print("=" * 60)
        
        # 过滤出成功的测试
        successful_results = [r for r in results if "error" not in r]
        failed_results = [r for r in results if "error" in r]
        
        if not successful_results:
            print("❌ 所有测试都失败了")
            return {"status": "failed", "results": results}
        
        # 计算平均分数
        avg_scores = {
            "overall": sum(r["overall_score"] for r in successful_results) / len(successful_results),
            "keyword_coverage": sum(r["keyword_coverage"] for r in successful_results) / len(successful_results),
            "file_relevance": sum(r["file_relevance"] for r in successful_results) / len(successful_results),
            "completeness": sum(r["completeness_score"] for r in successful_results) / len(successful_results),
            "accuracy": sum(r["accuracy_score"] for r in successful_results) / len(successful_results)
        }
        
        # 按类别分析
        categories = {}
        for result in successful_results:
            category = result["category"]
            if category not in categories:
                categories[category] = []
            categories[category].append(result["overall_score"])
        
        category_avg = {cat: sum(scores)/len(scores) for cat, scores in categories.items()}
        
        # 打印报告
        print(f"\n📈 总体表现:")
        print(f"   成功测试: {len(successful_results)}/{len(results)}")
        print(f"   失败测试: {len(failed_results)}")
        print(f"   综合评分: {avg_scores['overall']:.3f}")
        
        print(f"\n📊 详细指标:")
        print(f"   🔑 关键词覆盖率: {avg_scores['keyword_coverage']:.3f}")
        print(f"   📁 文件相关性: {avg_scores['file_relevance']:.3f}")
        print(f"   ✅ 完整性评分: {avg_scores['completeness']:.3f}")
        print(f"   🎯 准确性评分: {avg_scores['accuracy']:.3f}")
        
        print(f"\n🏷️ 分类表现:")
        for category, score in sorted(category_avg.items(), key=lambda x: x[1], reverse=True):
            print(f"   {category}: {score:.3f}")
        
        # 性能等级评估
        performance_level = self.get_performance_level(avg_scores['overall'])
        print(f"\n🏆 系统性能等级: {performance_level}")
        
        # 改进建议
        suggestions = self.generate_suggestions(avg_scores, failed_results)
        if suggestions:
            print(f"\n💡 改进建议:")
            for suggestion in suggestions:
                print(f"   • {suggestion}")
        
        # 保存详细报告
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": len(results),
                "successful_tests": len(successful_results),
                "failed_tests": len(failed_results),
                "average_scores": avg_scores,
                "category_scores": category_avg,
                "performance_level": performance_level
            },
            "detailed_results": results,
            "suggestions": suggestions
        }
        
        # 保存到文件
        report_file = f"rag_quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        return report_data
    
    def get_performance_level(self, score: float) -> str:
        """获取性能等级"""
        if score >= 0.9:
            return "🥇 优秀 (Excellent)"
        elif score >= 0.8:
            return "🥈 良好 (Good)"
        elif score >= 0.7:
            return "🥉 中等 (Fair)"
        elif score >= 0.6:
            return "⚠️ 需要改进 (Needs Improvement)"
        else:
            return "❌ 较差 (Poor)"
    
    def generate_suggestions(self, avg_scores: Dict[str, float], failed_results: List) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        if avg_scores['keyword_coverage'] < 0.7:
            suggestions.append("提高关键词覆盖率：优化提示词模板，确保回答包含更多相关技术术语")
        
        if avg_scores['file_relevance'] < 0.7:
            suggestions.append("改进文件检索：优化搜索算法，提高相关文件的召回率")
        
        if avg_scores['completeness'] < 0.7:
            suggestions.append("增强回答完整性：增加代码示例，提供更详细的实现步骤")
        
        if avg_scores['accuracy'] < 0.7:
            suggestions.append("提升回答准确性：优化检索模型，提高搜索结果的相似度阈值")
        
        if failed_results:
            suggestions.append(f"修复系统错误：{len(failed_results)}个测试失败，需要检查API服务和错误处理")
        
        if avg_scores['overall'] < 0.8:
            suggestions.append("考虑增加训练数据：扩充代码库，提高模型对特定领域的理解")
        
        return suggestions

def main():
    """主函数"""
    print("🎯 UniXcoder RAG系统质量评估")
    print("=" * 50)
    
    # 检查系统状态
    if not Path("faiss_index.bin").exists():
        print("❌ 未找到FAISS索引文件，请先运行: python build_index.py")
        return False
    
    if not Path("metadata.pkl").exists():
        print("❌ 未找到元数据文件，请先运行: python build_index.py")
        return False
    
    # 创建测试器并运行测试
    tester = RAGQualityTester()
    
    if not tester.api_service:
        print("❌ API服务初始化失败，无法进行测试")
        return False
    
    # 运行综合测试
    report = tester.run_comprehensive_test()
    
    # 返回测试结果
    return report.get("summary", {}).get("average_scores", {}).get("overall", 0) > 0.7

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)
